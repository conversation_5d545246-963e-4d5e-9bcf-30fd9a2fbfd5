package com.holderzone.member.common.enums.specials;

/**
 * <AUTHOR>
 * @date 2024/5/15
 * @description 限时特价活动状态
 */
public enum ActivityStateEnum {

    /**
     * 未发布
     */
    NOT_PUBLISH(0, "未发布"),

    /**
     * 进行中
     */
    RUNNING(1, "进行中"),

    /**
     * 已暂停
     */
    STOP(2, "已暂停"),

    /**
     * 已结束
     */
    OVER(3, "已结束"),

    /**
     * 未开始
     */
    NOT_START(4, "未开始"),
    ;


    private final int code;

    private final String des;

    ActivityStateEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public String getDes() {
        return this.des;
    }

    public int getCode() {
        return this.code;
    }


}
