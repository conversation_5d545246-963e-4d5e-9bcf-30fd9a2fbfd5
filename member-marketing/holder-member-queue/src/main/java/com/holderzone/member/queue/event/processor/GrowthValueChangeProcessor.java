package com.holderzone.member.queue.event.processor;

import com.google.common.base.Verify;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.event.GrowthValueChangeEvent;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.label.RelationLabelDTO;
import com.holderzone.member.queue.dto.member.MemberGrowthValueEvent;
import com.holderzone.member.queue.entity.HsaOperationMemberInfo;
import com.holderzone.member.queue.service.grade.HsaMemberGradeChangeDetailService;
import com.holderzone.member.queue.service.label.HsaLabelSettingService;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaOperationMemberInfoService;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 成长值变更事件处理
 *
 * <AUTHOR>
 * @version 1.0
 * @className GrowthValueChangProcessor
 * @description 成长值变更事件处理
 */
public class GrowthValueChangeProcessor extends AbstractProcessor<GrowthValueChangeEvent> {

    private final HsaOperationMemberInfoService hsaOperationMemberInfoService;
    private final HsaGrowthValueDetailService hsaGrowthValueDetailService;
    private final HsaLabelSettingService hsaLabelSettingService;

    private final HsaMemberGradeChangeDetailService hsaMemberGradeChangeDetailService;

    public GrowthValueChangeProcessor(GrowthValueChangeEvent event) {
        super(event);
        hsaMemberGradeChangeDetailService = SpringContextUtils.getBean(HsaMemberGradeChangeDetailService.class);
        ;
        hsaOperationMemberInfoService = SpringContextUtils.getBean(HsaOperationMemberInfoService.class);
        hsaGrowthValueDetailService = SpringContextUtils.getBean(HsaGrowthValueDetailService.class);
        hsaLabelSettingService = SpringContextUtils.getBean(HsaLabelSettingService.class);
    }

    @Override
    protected String getLogTitle() {
        return "GrowthValueChange";
    }

    /**
     * 参数验证
     */
    @Override
    protected void validate() {
        Verify.verify(event != null, "参数错误");
        Verify.verify(event.getMemberInfoGuidList() != null, "会员不存在");
    }

    @Override
    protected void process() {
//        memberGrowthValueDetailsService.changeMemberGrowthValue(event);
//        // 成长值增加后，检查是否需要升级(升级导致的成长值变化不在这里处理)
//        if (event.getSource() != GrowthSourceEnum.SOURCE_WITHDRAWAL_OF_UPGRADE_REWARDS) {
//            CheckCardLevelChangeEvent upgradeEvent = new CheckCardLevelChangeEvent();
//            BeanUtils.copyProperties(event, upgradeEvent);
//            cardInfoService.checkCardUpgrade(upgradeEvent);
//
        if (Objects.isNull(event.getChangeType())) {
            // 成长值回收
            MemberGrowthValueEvent memberGrowthValueEvent = getEvent();
            MemberGradeChangeDTO changeDTO = MemberGradeChangeDTO.getMemberGradeChangeDTO(memberGrowthValueEvent);
            changeDTO.setHsaOperationMemberInfos(new ArrayList<>(memberGrowthValueEvent.getHsaOperationMemberInfoMap().values()));
            changeDTO.setIssuerType(NumberConstant.NUMBER_1);
            hsaMemberGradeChangeDetailService.memberGradeChange(changeDTO);
            return;
        }
        // 成长值任务调整成长值
        if (event.getChangeType() == SumValueChangeEnum.TASK.getCode()) {
            MemberGrowthValueEvent memberGrowthValueEvent = getEvent();
            switch (event.getTaskType()) {
                //基础任务
                case NumberConstant.NUMBER_0:
                    hsaGrowthValueDetailService.baseTaskBusinessProcessor(memberGrowthValueEvent);
                    break;
                //消费任务
                case NumberConstant.NUMBER_1:
                    hsaGrowthValueDetailService.consumptionTaskBusinessProcessor(memberGrowthValueEvent);
                    break;
                //充值任务
                case NumberConstant.NUMBER_2:
                    hsaGrowthValueDetailService.rechargeTaskBusinessProcessor(memberGrowthValueEvent);
                    break;
            }
        }

        // 系统手动调整成长值
        if (event.getChangeType() == SumValueChangeEnum.SYSTEM.getCode()
                || event.getChangeType() == SumValueChangeEnum.INITIAL.getCode()
                || event.getChangeType() == SumValueChangeEnum.REDEEM_CODE.getCode()
                || event.getChangeType() == SumValueChangeEnum.RECHARGE_GIFT.getCode()
                || event.getChangeType() == SumValueChangeEnum.RECHARGE_REFUND.getCode()) {
            MemberGrowthValueEvent memberGrowthValueEvent = getEvent();
            hsaOperationMemberInfoService.systemUpdateMemberGrowth(memberGrowthValueEvent);
        }
        //关联标签
        hsaLabelSettingService.relationLabel(new RelationLabelDTO(event.getUserInfo(),
                event.getMemberInfoGuidList().stream().collect(Collectors.toSet())));
    }

    private MemberGrowthValueEvent getEvent() {
        MemberGrowthValueEvent memberGrowthValueEvent = new MemberGrowthValueEvent();
        BeanUtils.copyProperties(event, memberGrowthValueEvent);
        memberGrowthValueEvent.setStoreName(event.getStoreName());
        memberGrowthValueEvent.setStoreGuid(event.getStoreGuid());
        memberGrowthValueEvent.setHeaderUserInfo(event.getUserInfo());
        memberGrowthValueEvent.setOrderNumber(event.getOrderNumber());
        memberGrowthValueEvent.setOrderAmount(event.getOrderAmount());
        memberGrowthValueEvent.setOrderType(event.getOrderType());
        memberGrowthValueEvent.setCardGuid(event.getCardGuid());
        Map<String, HsaOperationMemberInfo> hsaOperationMemberInfoMap = hsaOperationMemberInfoService.checkMemberGuid(memberGrowthValueEvent.getMemberInfoGuidList())
                .stream()
                .collect(Collectors.toMap(HsaOperationMemberInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        memberGrowthValueEvent.setHsaOperationMemberInfoMap(hsaOperationMemberInfoMap);
        memberGrowthValueEvent.setDateTime(LocalDateTime.now());
        memberGrowthValueEvent.setMemberConsumptionGuid(event.getMemberConsumptionGuid());
        memberGrowthValueEvent.setOperSubjectGuid(event.getOperSubjectGuid());
        memberGrowthValueEvent.setOperatorAccountName(event.getOperatorAccountName());
        return memberGrowthValueEvent;
    }
}
