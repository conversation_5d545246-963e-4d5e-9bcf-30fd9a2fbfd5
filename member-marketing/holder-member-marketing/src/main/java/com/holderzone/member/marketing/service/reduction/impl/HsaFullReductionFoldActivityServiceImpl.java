package com.holderzone.member.marketing.service.reduction.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.FullReductionFoldActivityDTO;
import com.holderzone.member.common.dto.activity.FullReductionFoldDTO;
import com.holderzone.member.common.dto.activity.SettleApplyCommodityDTO;
import com.holderzone.member.common.dto.base.QueryArrayShopBase;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.reduction.FullReductionFoldActivityItemDTO;
import com.holderzone.member.common.dto.reduction.FullReductionFoldStoreDTO;
import com.holderzone.member.common.dto.specials.DateTimeHandleBO;
import com.holderzone.member.common.dto.specials.LimitSpecialsActivityItemDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import com.holderzone.member.common.enums.ApplyCommodityEnum;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum;
import com.holderzone.member.common.enums.gift.GiftThresholdEnum;
import com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum;
import com.holderzone.member.common.enums.order.OrderStateEnum;
import com.holderzone.member.common.enums.specials.ActivityStateEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityPageQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.qo.gift.ConsumptionGiftDetailsDTO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.member.ResponseOperationMemberInfo;
import com.holderzone.member.common.qo.specials.CheckActivityQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsCommodityRecordQO;
import com.holderzone.member.common.support.SettlementSupport;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.activity.*;
import com.holderzone.member.common.vo.card.OperationMemberInfoVO;
import com.holderzone.member.common.vo.grade.EffectiveTimeVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRunVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityStaticsVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityVO;
import com.holderzone.member.common.vo.specials.QueryLimitSpecialsActivityVO;
import com.holderzone.member.marketing.assembler.FullReductionFoldActivityAssembler;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivity;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivityItem;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivityRecord;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivityStore;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivity;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityItem;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityStore;
import com.holderzone.member.marketing.helper.ActivityTimeHelper;
import com.holderzone.member.marketing.manage.voting.helper.ActivityStatusHelper;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityItemMapper;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityMapper;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityRecordMapper;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityStoreMapper;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityItemService;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityRecordService;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityService;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityStoreService;
import com.holderzone.member.marketing.service.reduction.util.FullReductionFoldActivityCheck;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 满减满折活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaFullReductionFoldActivityServiceImpl
        extends HolderBaseServiceImpl<HsaFullReductionFoldActivityMapper, HsaFullReductionFoldActivity>
        implements HsaFullReductionFoldActivityService {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaFullReductionFoldActivityStoreService activityStoreService;

    @Resource
    private FullReductionFoldActivityCheck foldActivityCheck;

    @Resource
    private HsaFullReductionFoldActivityItemService activityItemService;

    @Resource
    private HsaFullReductionFoldActivityStoreMapper storeMapper;

    @Resource
    private HsaFullReductionFoldActivityItemMapper itemMapper;

    @Resource
    private HsaFullReductionFoldActivityRecordService recordService;

    @Resource(name = "marketingThreadExecutor")
    private Executor marketingThreadExecutor;

    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private SettlementSupport settlementSupport;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private HsaFullReductionFoldActivityRecordMapper activityRecordMapper;

    @Override
    public boolean saveOrUpdateActivity(FullReductionFoldActivityQO activityQO) {
        HsaFullReductionFoldActivity activity = FullReductionFoldActivityAssembler.activityReqDTO2Activity(activityQO);
        if (StringUtils.isEmpty(activity.getGuid())) {
            String activityGuid = guidGeneratorUtil.getStringGuid(HsaLimitSpecialsActivity.class.getSimpleName());
            activity.setGuid(activityGuid);
            activity.setActivityCode(activityGuid.substring(activityGuid.length() - 6));
            activity.setIsDelete(BooleanEnum.FALSE.getCode());
            baseMapper.insert(activity);
        } else {
            HsaFullReductionFoldActivity hsaLimitSpecialsActivity = queryByGuid(activity.getGuid());
            activity.setState(ActivityStateEnum.NOT_PUBLISH.getCode());
            activity.setActivityCode(hsaLimitSpecialsActivity.getActivityCode());
            BeanUtils.copyProperties(activity, hsaLimitSpecialsActivity);
            baseMapper.updateByGuid(hsaLimitSpecialsActivity);
        }

        // 门店关联
        saveActivityStore(activityQO, activity);

        // 商品关联
        saveActivityItem(activityQO, activity);
        return true;
    }

    private void saveActivityStore(FullReductionFoldActivityQO activityQO, HsaFullReductionFoldActivity specialsActivity) {
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == activityQO.getIsAllStore() && !CollectionUtils.isEmpty(activityQO.getStoreDTOList())) {
            List<FullReductionFoldStoreDTO> storeDTOList = activityQO.getStoreDTOList();
            List<HsaFullReductionFoldActivityStore> storeDOList = new ArrayList<>();
            List<String> guids = guidGeneratorUtil.getGuids(HsaLimitSpecialsActivityStore.class.getSimpleName(), storeDTOList.size());
            for (int i = 0; i < storeDTOList.size(); i++) {
                FullReductionFoldStoreDTO storeDTO = storeDTOList.get(i);
                HsaFullReductionFoldActivityStore activityStore = new HsaFullReductionFoldActivityStore();
                activityStore.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
                activityStore.setActivityGuid(specialsActivity.getGuid());
                activityStore.setStoreGuid(storeDTO.getStoreGuid());
                activityStore.setStoreName(storeDTO.getStoreName());
                activityStore.setStoreCode(storeDTO.getStoreNumber());
                activityStore.setGuid(guids.get(i));
                activityStore.setSystem(storeDTO.getSystem());
                storeDOList.add(activityStore);
            }
            activityStoreService.remove(new LambdaQueryWrapper<HsaFullReductionFoldActivityStore>()
                    .eq(HsaFullReductionFoldActivityStore::getActivityGuid, specialsActivity.getGuid())
                    .eq(HsaFullReductionFoldActivityStore::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
            );
            activityStoreService.saveBatch(storeDOList);
        }
    }

    private void saveActivityItem(FullReductionFoldActivityQO activityQO, HsaFullReductionFoldActivity specialsActivity) {
        activityItemService.remove(new LambdaQueryWrapper<HsaFullReductionFoldActivityItem>()
                .eq(HsaFullReductionFoldActivityItem::getActivityGuid, specialsActivity.getGuid())
                .eq(HsaFullReductionFoldActivityItem::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
        );

        if (activityQO.getApplyCommodity() == ApplyCommodityEnum.ALL.getCode()) {
            return;
        }
        List<FullReductionFoldActivityItemDTO> itemDTOList = activityQO.getItemDTOList();
        List<HsaFullReductionFoldActivityItem> itemDOList = Lists.newArrayList();
        List<String> guids = guidGeneratorUtil.getGuids(HsaLimitSpecialsActivityItem.class.getSimpleName(), itemDTOList.size());
        for (int i = 0, itemDTOListSize = itemDTOList.size(); i < itemDTOListSize; i++) {
            FullReductionFoldActivityItemDTO itemDTO = itemDTOList.get(i);
            HsaFullReductionFoldActivityItem item = new HsaFullReductionFoldActivityItem();
            item.setGuid(guids.get(i));
            item.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            item.setActivityGuid(specialsActivity.getGuid());
            item.setCommodityId(itemDTO.getCommodityId());
            item.setCommodityCode(itemDTO.getCommodityCode());
            item.setCommodityType(itemDTO.getCommodityType());
            item.setCommodityName(itemDTO.getCommodityName());
            item.setChannel(itemDTO.getChannel());
            item.setSystem(itemDTO.getSystem());
            itemDOList.add(item);
        }
        activityItemService.saveBatch(itemDOList);
    }

    /**
     * 推送活动
     *
     */
    public void sendSettlement(FullReductionFoldDTO fullReduction, Boolean isDelete) {

        //推送到优惠结算台
        SettlementDiscountDTO discountDTO = new SettlementDiscountDTO();
        discountDTO.setOperSubjectGuid(fullReduction.getOperSubjectGuid());
        final SettlementDiscountOptionEnum anEnum = SettlementDiscountOptionEnum.FULL_OFF;
        discountDTO.setOption(anEnum.getCode());
        if (Boolean.TRUE.equals(isDelete)) {
            //删除
            discountDTO.setDelDiscountGuids(Collections.singletonList(fullReduction.getActivityCode()));
        } else {
            //新增、更新
            SettlementDiscountDTO.Discount discount = new SettlementDiscountDTO.Discount().
                    setDiscountType(anEnum.getType().getCode())
                    .setDiscountNum(anEnum.getLimitNum())
                    .setDiscountGuid(fullReduction.getActivityCode())
                    .setRelationRule(fullReduction.getRelationRule())
                    .setDiscountName(fullReduction.getName())
                    .setIsFirstAdd(fullReduction.getIsPublish() == BooleanEnum.TRUE.getCode() ?
                            BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode());
            List<FullReductionFoldActivityDTO> fullReductionFoldActivityDTOS = JSON.parseArray(fullReduction.getFullReductionFoldJson(), FullReductionFoldActivityDTO.class);

            StringBuilder strBuilder = getDiscountDynamicBuilder(fullReduction, fullReductionFoldActivityDTOS);
            discount.setDiscountDynamic(strBuilder.toString());
            discountDTO.setDiscountList(Collections.singletonList(discount));
        }
        settlementSupport.syncSettlementDiscount(discountDTO);
    }

    private static StringBuilder getDiscountDynamicBuilder(FullReductionFoldDTO fullReduction,
                                                           List<FullReductionFoldActivityDTO> fullReductionFoldActivityDTOS) {
        StringBuilder strBuilder = new StringBuilder();
        for (int i = 0; i < fullReductionFoldActivityDTOS.size(); i++) {
            FullReductionFoldActivityDTO fullReductionFoldActivityDTO = fullReductionFoldActivityDTOS.get(i);
            if (fullReduction.getFullReductionTacticsType() == 1) {
                strBuilder.append(GiftThresholdEnum.THRESHOLD_EVERY_FULL.getDes());
            } else {
                strBuilder.append(GiftThresholdEnum.THRESHOLD_FULL.getDes());
            }

            strBuilder.append(fullReductionFoldActivityDTO.getConsumptionMoney()).append("元");
            if (fullReductionFoldActivityDTO.getConsumptionType() == 1) {
                strBuilder.append("打").append(fullReductionFoldActivityDTO.getConsumptionNumber()).append("折");
            } else {
                strBuilder.append("减").append(fullReductionFoldActivityDTO.getConsumptionNumber()).append("元");
            }
            if (i != fullReductionFoldActivityDTOS.size() - 1) {
                // 这是最后一次循环
                strBuilder.append(",");
            }
        }
        return strBuilder;
    }

    @Override
    public PageResult<FullReductionFoldActivityPageVO> pageQuery(FullReductionFoldActivityPageQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<FullReductionFoldActivityPageVO> activityVOList = baseMapper.query(qo);

        if (CollectionUtils.isEmpty(activityVOList)) {
            log.warn("满减满折活动列表为空");
            return PageUtil.pageResult(new PageInfo<>(activityVOList));
        }
        List<String> activityGuidList = activityVOList.stream()
                .map(FullReductionFoldActivityPageVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // 活动订单数
        Map<String, Long> activityRecrodMap = queryActivityRecord(activityGuidList);
        for (FullReductionFoldActivityPageVO detailsVO : activityVOList) {
            detailsVO.setState(ActivityStatusHelper.handleLimitSpecialsActivityState(detailsVO.getState(), detailsVO.getStartTime(), detailsVO.getEndTime()));
            detailsVO.setActivityOrder(activityRecrodMap.getOrDefault(detailsVO.getGuid(), 0L));
        }

        return PageUtil.getPageResult(new PageInfo<>(activityVOList));
    }

    /**
     * 查询活动订单数
     */
    private Map<String, Long> queryActivityRecord(List<String> activityGuidList) {
        List<FullReductionFoldActivityStaticsVO> staticsList = recordService.staticsOrderCountByActivityGuid(activityGuidList);
        return staticsList.stream()
                .collect(Collectors.toMap(FullReductionFoldActivityStaticsVO::getActivityGuid, FullReductionFoldActivityStaticsVO::getOrderCount, (key1, key2) -> key1));
    }

    public static final String NOT_ACTIVITY = "活动不存在";

    @Override
    public FullReductionFoldActivityVO queryActivity(String guid) {
        HsaFullReductionFoldActivity activity = baseMapper.queryByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        FullReductionFoldActivityVO detailsVO = FullReductionFoldActivityAssembler.activity2ActivityDetailsVO(activity);
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 使子线程也能获取到 requestAttributes
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(requestAttributes, true);

        // 适用于部分门店查询门店关联
        CompletableFuture<List<FullReductionFoldStoreDTO>> storeFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleStoreInfo(detailsVO.getIsAllStore(), detailsVO.getGuid());
        }, marketingThreadExecutor).exceptionally(throwable -> {
            log.error("查询适用门店失败", throwable);
            throw new MemberMarketingException("无法获取适用门店");
        });

        // 指定会员
        CompletableFuture<ConsumptionGiftDetailsDTO> memberFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            UserContextUtils.put(JacksonUtils.writeValueAsString(headerUserInfo));
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleMemberInfo(detailsVO.getMemberDTO());
        }, marketingThreadExecutor).exceptionally(throwable -> {
            log.error("查询指定会员失败", throwable);
            throw new MemberMarketingException("无法获取指定会员");
        });

        // 活动商品
        CompletableFuture<List<FullReductionFoldActivityItemDTO>> itemFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            UserContextUtils.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleItemInfo(guid);
        }, marketingThreadExecutor).exceptionally(throwable -> {
            log.error("查询活动商品失败", throwable);
            throw new MemberMarketingException("无法获取活动商品");
        });

        CompletableFuture<Void> all = CompletableFuture.allOf(storeFuture, memberFuture, itemFuture);
        try {
            all.get();
            detailsVO.setStoreDTOList(storeFuture.get());
            detailsVO.setMemberDTO(memberFuture.get());
            detailsVO.setItemDTOList(itemFuture.get());
        } catch (Exception e) {
            log.error("查询活动详情失败", e);
            Thread.currentThread().interrupt();
            throw new MemberMarketingException("系统繁忙稍后再试");
        }

        return detailsVO;
    }

    private List<FullReductionFoldActivityItemDTO> handleItemInfo(String guid) {
        List<HsaFullReductionFoldActivityItem> activityItems = getFullReductionFoldActivityItems(guid);
        if (CollUtil.isEmpty(activityItems)) {
            return Collections.emptyList();
        }

        // 按 system 分组，分别查详情并合并
        Map<String, List<HsaFullReductionFoldActivityItem>> systemMap = activityItems.stream()
                .filter(c -> c.getSystem() != null)
                .collect(Collectors.groupingBy(HsaFullReductionFoldActivityItem::getSystem));

        List<ResCommodityBase> allCommodityDetails = systemMap.entrySet().stream()
                .flatMap(entry -> {
                    String systemName = entry.getKey();
                    List<String> ids = entry.getValue().stream()
                            .map(HsaFullReductionFoldActivityItem::getCommodityId)
                            .distinct()
                            .collect(Collectors.toList());
                    List<ResCommodityBase> details = externalSupport
                            .itemServer(SystemEnum.getSystemCodeBySystemName(systemName))
                            .listCommodityByDetail(new QueryArrayShopBase().setCommodityIdList(ids));
                    return CollUtil.isEmpty(details) ? java.util.stream.Stream.empty() : details.stream();
                })
                .collect(Collectors.toList());

        // 组装返回结果
        return activityItems.stream()
                .map(item -> getResponseCouponCommodityVO(item, allCommodityDetails))
                .collect(Collectors.toList());
    }

    private FullReductionFoldActivityItemDTO getResponseCouponCommodityVO(HsaFullReductionFoldActivityItem item, List<ResCommodityBase> allCommodityDetails) {
        FullReductionFoldActivityItemDTO fullReductionFoldActivityItemDTO = new FullReductionFoldActivityItemDTO();
        if (CollUtil.isEmpty(allCommodityDetails)) {
            buildRespCommodityByNull(item, fullReductionFoldActivityItemDTO);
        } else {
            Map<String, ResCommodityBase> commodityBaseMap = allCommodityDetails.stream()
                    .collect(Collectors.toMap(ResCommodityBase::getCommodityId, resCommodityBase -> resCommodityBase, (k1, k2) -> k1));
            ResCommodityBase commodityBase = commodityBaseMap.get(item.getCommodityId());
            if (commodityBase == null) {
                buildRespCommodityByNull(item, fullReductionFoldActivityItemDTO);
            } else {
                buildRespCommodityByBase(item, fullReductionFoldActivityItemDTO, commodityBase);
            }
        }
        return fullReductionFoldActivityItemDTO;
    }

    private void buildRespCommodityByNull(HsaFullReductionFoldActivityItem item,
                                          FullReductionFoldActivityItemDTO fullReductionFoldActivityItemDTO) {
        fullReductionFoldActivityItemDTO.setActivityGuid(item.getActivityGuid());
        fullReductionFoldActivityItemDTO.setCommodityCode(item.getCommodityCode());
        fullReductionFoldActivityItemDTO.setCommodityId(item.getCommodityId());
        fullReductionFoldActivityItemDTO.setCommodityName(item.getCommodityName());
        fullReductionFoldActivityItemDTO.setCommodityType(item.getCommodityType());
        fullReductionFoldActivityItemDTO.setChannel(item.getChannel());
        fullReductionFoldActivityItemDTO.setIsExist(BooleanEnum.FALSE.getCode());
        fullReductionFoldActivityItemDTO.setSystem(item.getSystem());
    }

    private void buildRespCommodityByBase(HsaFullReductionFoldActivityItem item,
                                          FullReductionFoldActivityItemDTO fullReductionFoldActivityItemDTO,
                                          ResCommodityBase commodityBase) {
        fullReductionFoldActivityItemDTO.setActivityGuid(item.getActivityGuid());
        fullReductionFoldActivityItemDTO.setCommodityCode(commodityBase.getCommodityCode());
        fullReductionFoldActivityItemDTO.setCommodityId(item.getCommodityId());
        fullReductionFoldActivityItemDTO.setCommodityName(commodityBase.getName());
        fullReductionFoldActivityItemDTO.setCommodityType(item.getCommodityType());
        fullReductionFoldActivityItemDTO.setChannel(item.getChannel());
        fullReductionFoldActivityItemDTO.setIsExist(BooleanEnum.TRUE.getCode());
        fullReductionFoldActivityItemDTO.setSystem(item.getSystem());
    }

    private List<HsaFullReductionFoldActivityItem> getFullReductionFoldActivityItems(String guid) {
        return itemMapper.selectList(new LambdaQueryWrapper<HsaFullReductionFoldActivityItem>()
                .eq(HsaFullReductionFoldActivityItem::getActivityGuid, guid)
                .eq(HsaFullReductionFoldActivityItem::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    private List<FullReductionFoldStoreDTO> handleStoreInfo(Integer isAllStore, String guid) {
        List<FullReductionFoldStoreDTO> storeDTOList = new ArrayList<>();
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == isAllStore) {
            List<HsaFullReductionFoldActivityStore> storeList = getFullReductionFoldActivityStores(guid);
            if (!CollectionUtils.isEmpty(storeList)) {
                storeList.forEach(store -> {
                    FullReductionFoldStoreDTO storeDTO = new FullReductionFoldStoreDTO();
                    storeDTO.setStoreGuid(store.getStoreGuid());
                    storeDTO.setStoreNumber(store.getStoreCode());
                    storeDTO.setStoreName(store.getStoreName());
                    storeDTO.setSystem(store.getSystem());
                    storeDTOList.add(storeDTO);
                });
                return storeDTOList;
            }
        }
        return storeDTOList;
    }

    private List<HsaFullReductionFoldActivityStore> getFullReductionFoldActivityStores(String guid) {
        return storeMapper.selectList(new LambdaQueryWrapper<HsaFullReductionFoldActivityStore>()
                .eq(HsaFullReductionFoldActivityStore::getActivityGuid, guid)
                .eq(HsaFullReductionFoldActivityStore::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
    }

    private ConsumptionGiftDetailsDTO handleMemberInfo(ConsumptionGiftDetailsDTO memberDTO) {
        if (!ObjectUtils.isEmpty(memberDTO)) {
            List<String> guidList = memberDTO.getGuidList();
            RequestQueryMemberBaseQO memberBaseQO = new RequestQueryMemberBaseQO();
            memberBaseQO.setMemberGuidList(guidList);
            memberBaseQO.setPageSize(guidList.size());
            memberBaseQO.setCurrentPage(1);
            com.holderzone.framework.util.Page<ResponseOperationMemberInfo> memberInfoPage =
                    externalSupport.memberServer(ThreadLocalCache.getSystem()).getOperationMemberInfoPage(memberBaseQO);
            log.info("[满减满折活动]查询会员信息={}", JacksonUtils.writeValueAsString(memberInfoPage));
            List<ResponseOperationMemberInfo> memberInfoList = memberInfoPage.getData();
            if (CollUtil.isNotEmpty(memberInfoList)) {
                List<OperationMemberInfoVO> memberInfoGuidList = getMemberInfoVOList(memberInfoList);
                memberDTO.setMemberInfoGuidList(memberInfoGuidList);
            }
        }
        return memberDTO;
    }

    private List<OperationMemberInfoVO> getMemberInfoVOList(List<ResponseOperationMemberInfo> memberInfoList) {
        List<OperationMemberInfoVO> memberInfoGuidList = Lists.newArrayList();
        for (ResponseOperationMemberInfo memberInfo : memberInfoList) {
            OperationMemberInfoVO operationMemberInfoVO = new OperationMemberInfoVO();
            operationMemberInfoVO.setMemberAccount(memberInfo.getMemberAccount());
            operationMemberInfoVO.setPhoneNum(memberInfo.getPhoneNum());
            operationMemberInfoVO.setUserName(memberInfo.getUserName());
            operationMemberInfoVO.setGuid(memberInfo.getGuid());
            memberInfoGuidList.add(operationMemberInfoVO);
        }
        return memberInfoGuidList;
    }

    @Override
    public List<FullReductionFoldActivityPublishVO> updateState(String guid, Integer state) {
        HsaFullReductionFoldActivity activity = baseMapper.queryByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        if (state == ActivityStateEnum.RUNNING.getCode()) {
            FullReductionFoldDTO limitSpecialsActivityDTO = new FullReductionFoldDTO();
            limitSpecialsActivityDTO.setIsPublish(activity.getIsPublish());
            activity.setState(state);
            if (activity.getIsPublish() == BooleanEnum.FALSE.getCode()) {
                activity.setIsPublish(BooleanEnum.TRUE.getCode());
            }
            baseMapper.updateByGuid(activity);
            //同步结算台
            limitSpecialsActivityDTO.setActivityCode(activity.getActivityCode());
            limitSpecialsActivityDTO.setOperSubjectGuid(activity.getOperSubjectGuid());
            limitSpecialsActivityDTO.setName(activity.getActivityName());
            limitSpecialsActivityDTO.setRelationRule(activity.getRelationRule());
            limitSpecialsActivityDTO.setFullReductionFoldJson(activity.getFullReductionFoldJson());
            limitSpecialsActivityDTO.setFullReductionTacticsType(activity.getFullReductionTacticsType());
            sendSettlement(limitSpecialsActivityDTO, Boolean.FALSE);
        } else if (state == ActivityStateEnum.STOP.getCode()) {
            // 暂停
            activity.setState(state);
            baseMapper.updateByGuid(activity);
        } else {
            throw new MemberMarketingException("状态类型错误");
        }
        return Lists.newArrayList();

    }

    public List<FullReductionFoldActivityPublishVO> checkActivityPublish(String guid, HsaFullReductionFoldActivity activity) {
        // 发布/开启 判断当前活动中，是否存在相同时间内、相同场景、相同门店、相同商品组合重复
        CheckActivityQO checkActivityQO = getCheckActivityQO(guid, activity);
        log.info("[校验活动发布]checkActivityQO={}", JacksonUtils.writeValueAsString(checkActivityQO));
        List<FullReductionFoldActivityPublishVO> activityPublishList = baseMapper.checkActivity(checkActivityQO);
        log.info("[校验活动发布]activityPublishList={}", JacksonUtils.writeValueAsString(activityPublishList));
        if (!CollectionUtils.isEmpty(activityPublishList)) {
            // 校验活动时段
            return activityPublishList.stream().filter(pubActivity -> {
                if (Objects.equals(BooleanEnum.FALSE.getCode(), activity.getIsLimitPeriod())
                        || Objects.equals(BooleanEnum.FALSE.getCode(), pubActivity.getIsLimitPeriod())) {
                    log.warn("有活动为不限制活动时段则不限制");
                    return true;
                }
                String limitPeriodJson = activity.getLimitPeriodJson();
                String pubLimitPeriodJson = pubActivity.getLimitPeriodJson();
                if (StringUtils.isEmpty(limitPeriodJson) || StringUtils.isEmpty(pubLimitPeriodJson)) {
                    log.warn("限制时段限制类型json为空,当作不限制");
                    return true;
                }

                DateTimeHandleBO handleBO = getHandleBO(activity, limitPeriodJson);
                List<Pair<LocalDateTime, LocalDateTime>> currentActivityTimeList = ActivityTimeHelper.getDateTimePairs(handleBO);

                DateTimeHandleBO handlePubBO = getHandlePubBO(pubActivity, pubLimitPeriodJson);
                List<Pair<LocalDateTime, LocalDateTime>> dbActivityTimeList = ActivityTimeHelper.getDateTimePairs(handlePubBO);

                // 时间交集循环判断
                return ActivityTimeHelper.checkDateTimeIntersection(currentActivityTimeList, dbActivityTimeList);
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private DateTimeHandleBO getHandleBO(HsaFullReductionFoldActivity activity,
                                         String limitPeriodJson) {
        DateTimeHandleBO handleBO = new DateTimeHandleBO();
        handleBO.setTimeVOList(JacksonUtils.toObjectList(EffectiveTimeVO.class, limitPeriodJson));
        handleBO.setLimitPeriodType(activity.getLimitPeriodType());
        handleBO.setStartTime(activity.getStartTime());
        handleBO.setEndTime(activity.getEndTime());
        return handleBO;
    }

    private DateTimeHandleBO getHandlePubBO(FullReductionFoldActivityPublishVO pubActivity,
                                            String pubLimitPeriodJson) {
        DateTimeHandleBO handlePubBO = new DateTimeHandleBO();
        handlePubBO.setTimeVOList(JacksonUtils.toObjectList(EffectiveTimeVO.class, pubLimitPeriodJson));
        handlePubBO.setLimitPeriodType(pubActivity.getLimitPeriodType());
        handlePubBO.setStartTime(pubActivity.getStartTime());
        handlePubBO.setEndTime(pubActivity.getEndTime());
        return handlePubBO;
    }

    private CheckActivityQO getCheckActivityQO(String guid, HsaFullReductionFoldActivity activity) {
        CheckActivityQO checkActivityQO = new CheckActivityQO();
        checkActivityQO.setGuid(guid);
        checkActivityQO.setStartTime(activity.getStartTime());
        checkActivityQO.setEndTime(activity.getEndTime());
        checkActivityQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<String> commodityIdList = getCommodityIdList(guid);
        checkActivityQO.setCommodityIdList(commodityIdList);
        if (Objects.equals(GoodsApplicableStoreEnum.PORTION_STORE.getCode(), activity.getIsAllStore())) {
            List<String> storeIdList = getStoreIdList(guid);
            checkActivityQO.setStoreIdList(storeIdList);
        }
        if (Objects.equals(ApplyBusinessTypeEnum.APPOINT_BUSINESS.getCode(), activity.getApplyBusiness())) {
            List<String> applyBusinessList = com.google.common.collect.Lists.newArrayList(activity.getApplyBusinessJson().split(","));
            checkActivityQO.setApplyBusinessList(applyBusinessList);
        }
        return checkActivityQO;
    }

    private List<String> getStoreIdList(String guid) {
        List<HsaFullReductionFoldActivityStore> storeList = getFullReductionFoldActivityStores(guid);
        List<String> storeIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(storeList)) {
            storeIdList = storeList.stream()
                    .map(HsaFullReductionFoldActivityStore::getStoreGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return storeIdList;
    }

    private List<String> getCommodityIdList(String guid) {
        List<HsaFullReductionFoldActivityItem> activityItems = getFullReductionFoldActivityItems(guid);
        List<String> commodityIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(activityItems)) {
            commodityIdList = activityItems.stream()
                    .map(HsaFullReductionFoldActivityItem::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return commodityIdList;
    }

    @Override
    public boolean delete(String guid) {
        HsaFullReductionFoldActivity activity = baseMapper.queryByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }

        if (Objects.equals(BooleanEnum.TRUE.getCode(), activity.getIsPublish())) {
            throw new MemberMarketingException("已发布过的活动不可删除");
        }
        activity.setIsDelete(BooleanEnum.TRUE.getCode());
        baseMapper.updateByGuid(activity);
        //同步结算台
        FullReductionFoldDTO fullReduction = new FullReductionFoldDTO();
        BeanUtils.copyProperties(activity, fullReduction);
        sendSettlement(fullReduction, Boolean.TRUE);
        return true;
    }

    @Override
    public boolean checkActivityState(String guid, Integer state) {
        HsaFullReductionFoldActivity activity = baseMapper.queryByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            return Boolean.TRUE;
        }
        Integer activityState = ActivityStatusHelper.handleLimitSpecialsActivityState(
                activity.getState(), activity.getStartTime(), activity.getEndTime());
        return !Objects.equals(activityState, state);
    }

    @Override
    public List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(FullReductionFoldActivityRunQO query) {
        log.info("[结算台查询满减满折]query={}", JacksonUtils.writeValueAsString(query));

        List<QueryFullReductionFoldActivityVO> activityList = baseMapper.queryActivityByRun(query);
        log.info("[结算台查询满减满折]activityList={}", JacksonUtils.writeValueAsString(activityList));
        if (CollUtil.isEmpty(activityList)) {
            log.warn("未查询到匹配活动");
            return Lists.newArrayList();
        }
        LocalDateTime now = LocalDateTime.now();
        List<QueryFullReductionFoldActivityVO> specialsActivityList = getQueryActivityVOS(query, activityList, now);

        if (CollUtil.isEmpty(specialsActivityList)) {
            log.warn("校验未命中");
            return Lists.newArrayList();
        }

        return getSettleCommodity(query, specialsActivityList);
    }

    @Override
    public List<FullReductionFoldActivityRunVO> queryRunInfo(FullReductionFoldActivityRunQO query) {
        log.info("[查询进行中的活动信息]query={}", JacksonUtils.writeValueAsString(query));
        List<FullReductionFoldActivityRunVO> activityList = baseMapper.queryRunInfo(query);
        log.info("[查询进行中的活动信息]activityList={}", JacksonUtils.writeValueAsString(activityList));
        if (CollUtil.isEmpty(activityList)) {
            log.warn("未查询到匹配活动");
            return Lists.newArrayList();
        }

        // 组装活动数据,此处的处理只处理了此时需要的
        List<FullReductionFoldActivityRunVO> activityRunVOList = Lists.newArrayList();
        for (FullReductionFoldActivityRunVO activityVO : activityList) {
            FullReductionFoldActivityRunVO activityRunVO = new FullReductionFoldActivityRunVO();
            BeanUtils.copyProperties(activityVO, activityRunVO);
            activityRunVO.setActivityName(activityVO.getActivityName());
            activityRunVO.setActivityCode(activityVO.getActivityCode());
            activityRunVO.setIsEnabled(activityVO.getIsEnabled());
            if (!StringUtils.isEmpty(activityVO.getLabelGuidJson())) {
                List<String> labelGuidList = Lists.newArrayList(activityVO.getLabelGuidJson().split(","));
                activityRunVO.setLabelGuidList(labelGuidList);
            }
            activityRunVOList.add(activityRunVO);
        }
        return activityRunVOList;
    }

    private List<QueryFullReductionFoldActivityVO> getQueryActivityVOS(FullReductionFoldActivityRunQO query,
                                                                       List<QueryFullReductionFoldActivityVO> activityList,
                                                                       LocalDateTime now) {
        return activityList.stream().filter(activity -> {
            //人群校验
            if (foldActivityCheck.checkApplyTypeEnum(query, activity)) {
                return false;
            }
            if (foldActivityCheck.checkLimitPeriod(activity, now)) {
                activity.setIsEnabled(BooleanEnum.TRUE.getCode());
            } else {
                activity.setIsEnabled(BooleanEnum.FALSE.getCode());
            }
            Integer state = ActivityStatusHelper.handleLimitSpecialsActivityState(activity.getState(), activity.getStartTime(), activity.getEndTime());
            return state == ActivityStateEnum.RUNNING.getCode();
        }).collect(Collectors.toList());
    }

    private List<FullReductionFoldActivityRunVO> getSettleCommodity(FullReductionFoldActivityRunQO query,
                                                                    List<QueryFullReductionFoldActivityVO> specialsActivityList) {
        //获取活动商品
        Map<String, List<HsaFullReductionFoldActivityItem>> limitSpecialsItemGroupingMap = getFullReductionFoldGroupingMap(specialsActivityList, query.getOperSubjectGuid());

        List<FullReductionFoldActivityRunVO> limitSpecialsActivityItemVOS = Lists.newArrayList();

        Map<String, SettleApplyCommodityDTO> settleApplyCommodityDTOMap = query.getSettleApplyCommodityDTOS()
                .stream()
                .collect(Collectors.toMap(SettleApplyCommodityDTO::getCommodityCode, Function.identity(), (entity1, entity2) -> entity1));

        //组装活动数据
        for (QueryFullReductionFoldActivityVO activityVO : specialsActivityList) {
            FullReductionFoldActivityRunVO fullReductionFoldActivityRunVO = new FullReductionFoldActivityRunVO();
            BeanUtils.copyProperties(activityVO, fullReductionFoldActivityRunVO);
            fullReductionFoldActivityRunVO.setActivityName(activityVO.getActivityName());
            fullReductionFoldActivityRunVO.setActivityCode(activityVO.getActivityCode());
            fullReductionFoldActivityRunVO.setIsEnabled(activityVO.getIsEnabled());
            fullReductionFoldActivityRunVO.setApplyCommodity(activityVO.getApplyCommodity());
            if (!StringUtils.isEmpty(activityVO.getLabelGuidJson())) {
                List<String> labelGuidList = Lists.newArrayList(activityVO.getLabelGuidJson().split(","));
                fullReductionFoldActivityRunVO.setLabelGuidList(labelGuidList);
            }
            //匹配商品列表
            matchingCommodity(activityVO, limitSpecialsItemGroupingMap, settleApplyCommodityDTOMap, fullReductionFoldActivityRunVO);
            limitSpecialsActivityItemVOS.add(fullReductionFoldActivityRunVO);
        }
        return limitSpecialsActivityItemVOS;
    }

    private void matchingCommodity(QueryFullReductionFoldActivityVO activityVO,
                                   Map<String, List<HsaFullReductionFoldActivityItem>> limitSpecialsItemGroupingMap,
                                   Map<String, SettleApplyCommodityDTO> settleApplyCommodityDTOMap,
                                   FullReductionFoldActivityRunVO fullReductionFoldActivityRunVO) {
        List<HsaFullReductionFoldActivityItem> limitSpecialsActivityItemList = Lists.newArrayList();
        if (activityVO.getApplyCommodity() == ApplyCommodityEnum.ALL.getCode()) {
            return;
        }
        if (limitSpecialsItemGroupingMap.containsKey(activityVO.getGuid())) {
            List<HsaFullReductionFoldActivityItem> hsaLimitSpecialsActivityItems = limitSpecialsItemGroupingMap.get(activityVO.getGuid());
            for (HsaFullReductionFoldActivityItem hsaLimitSpecialsActivityItem : hsaLimitSpecialsActivityItems) {
                if (settleApplyCommodityDTOMap.containsKey(hsaLimitSpecialsActivityItem.getCommodityCode())) {
                    limitSpecialsActivityItemList.add(hsaLimitSpecialsActivityItem);
                }
            }

            if (CollUtil.isEmpty(limitSpecialsActivityItemList)) {
                fullReductionFoldActivityRunVO.setIsEnabled(BooleanEnum.FALSE.getCode());
            }
            //组装商品数据
            foldActivityCheck.checkLimitItem(
                    activityVO,
                    limitSpecialsItemGroupingMap,
                    fullReductionFoldActivityRunVO);
        }
    }

    private Map<String, List<HsaFullReductionFoldActivityItem>> getFullReductionFoldGroupingMap(List<QueryFullReductionFoldActivityVO> specialsActivityList,
                                                                                                String operSubjectGuid) {

        List<HsaFullReductionFoldActivityItem> hsaLimitSpecialsActivityItems = getFullReductionFoldActivityItems(specialsActivityList, operSubjectGuid);

        //获取活动商品
        return hsaLimitSpecialsActivityItems
                .stream()
                .collect(Collectors.groupingBy(HsaFullReductionFoldActivityItem::getActivityGuid));
    }

    private List<HsaFullReductionFoldActivityItem> getFullReductionFoldActivityItems(List<QueryFullReductionFoldActivityVO> specialsActivityList,
                                                                                     String operSubjectGuid) {
        //计算剩余限购
        List<String> activityGuidList = specialsActivityList
                .stream()
                .map(QueryFullReductionFoldActivityVO::getGuid)
                .collect(Collectors.toList());

        return itemMapper.selectList(new LambdaQueryWrapper<HsaFullReductionFoldActivityItem>()
                .eq(HsaFullReductionFoldActivityItem::getOperSubjectGuid, operSubjectGuid)
                .in(HsaFullReductionFoldActivityItem::getActivityGuid, activityGuidList));
    }
}
