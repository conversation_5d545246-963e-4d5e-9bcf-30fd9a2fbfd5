package com.holderzone.member.marketing.controller.full;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynMarketingDTO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityPageQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityPageVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityPublishVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityVO;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 满减满折
 **/
@Api(tags = "满减满折")
@RestController
@RequestMapping("/full/reduction/fold")
@AllArgsConstructor
public class HsaFullReductionFoldActivityController {

    @Resource
    private HsaFullReductionFoldActivityService activeService;

    @ApiOperation("新增or编辑限时特价活动")
    @PostMapping("/save_or_update")
    public Result<Boolean> saveOrUpdateActivity(@RequestBody FullReductionFoldActivityQO activityQO) {
        return Result.success(activeService.saveOrUpdateActivity(activityQO));
    }

    @PostMapping("/page")
    public Result<PageResult<FullReductionFoldActivityPageVO>> pageQuery(@RequestBody FullReductionFoldActivityPageQO qo) {
        return Result.success(activeService.pageQuery(qo));
    }

    @GetMapping("/get")
    Result<FullReductionFoldActivityVO> queryActivity(String guid) {
        return Result.success(activeService.queryActivity(guid));
    }

    @GetMapping("/update_state")
    Result<List<FullReductionFoldActivityPublishVO>> updateState(@RequestParam("guid") String guid,
                                                                 @RequestParam("state") Integer state) {
        return Result.success(activeService.updateState(guid, state));
    }

    @DeleteMapping("/delete")
    Result<Boolean> delete(String guid) {
        return Result.success(activeService.delete(guid));
    }

    @GetMapping("/check_activity_state")
    public Result<Boolean> checkActivityState(@RequestParam("guid") String guid,
                                              @RequestParam("state") Integer state) {
        return Result.success(activeService.checkActivityState(guid, state));
    }

    /**
     * 结算台查询活动
     */
    @PostMapping("/query_settle_commodity_run")
    List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(@RequestBody FullReductionFoldActivityRunQO query) {
        return activeService.querySettleCommodityByRun(query);
    }

    @PostMapping("/query_run_info")
    List<FullReductionFoldActivityRunVO> queryRunInfo(@RequestBody FullReductionFoldActivityRunQO query){
        return activeService.queryRunInfo(query);
    }
}
