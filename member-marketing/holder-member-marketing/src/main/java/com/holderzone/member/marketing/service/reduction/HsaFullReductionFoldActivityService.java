package com.holderzone.member.marketing.service.reduction;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynMarketingDTO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityPageQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsActivityRunQO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityPageVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityPublishVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityPublishVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRunVO;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivity;

import io.swagger.annotations.ApiOperation;
import lombok.experimental.Accessors;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 * @description 满减满折活动
 */
@Accessors(chain = true)
public interface HsaFullReductionFoldActivityService extends IHolderBaseService<HsaFullReductionFoldActivity> {

    boolean saveOrUpdateActivity(FullReductionFoldActivityQO activityQO);

    PageResult<FullReductionFoldActivityPageVO> pageQuery(FullReductionFoldActivityPageQO qo);

    FullReductionFoldActivityVO queryActivity(String guid);

    List<FullReductionFoldActivityPublishVO> updateState(String guid, Integer state);

    boolean delete(String guid);

    boolean checkActivityState(String guid, Integer state);

    /**
     * 结算台查询活动
     */
    List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(FullReductionFoldActivityRunQO query);

    List<FullReductionFoldActivityRunVO> queryRunInfo(FullReductionFoldActivityRunQO query);
}
